using UnityEngine;
using System.Reflection;

/// <summary>
/// Comprehensive diagnostic tool for player controller, gravity, and input issues
/// </summary>
public class PlayerSystemDiagnostic : MonoBehaviour
{
    [Header("Debug Controls")]
    [SerializeField] private KeyCode diagnosticKey = KeyCode.F1;
    [SerializeField] private KeyCode fixKey = KeyCode.F2;
    [SerializeField] private bool enableContinuousDebug = false;
    
    private PlayerController player;
    private GravityAffector gravityAffector;
    private Ship currentShip;
    
    void Start()
    {
        player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            gravityAffector = player.GetComponent<GravityAffector>();
        }
        currentShip = FindObjectOfType<Ship>();
    }
    
    void Update()
    {
        if (Input.GetKeyDown(diagnosticKey))
        {
            RunFullDiagnostic();
        }
        
        if (Input.GetKeyDown(fixKey))
        {
            ApplyQuickFixes();
        }
        
        if (enableContinuousDebug)
        {
            ContinuousDebug();
        }
    }
    
    void RunFullDiagnostic()
    {
        Debug.Log("=== PLAYER SYSTEM DIAGNOSTIC ===");
        
        DiagnoseGravitySystem();
        DiagnoseGroundDetection();
        DiagnoseInputSystem();
        DiagnoseShipGravity();
        
        Debug.Log("=== END DIAGNOSTIC ===");
    }
    
    void DiagnoseGravitySystem()
    {
        Debug.Log("--- GRAVITY SYSTEM ---");
        
        if (gravityAffector == null)
        {
            Debug.LogError("❌ GravityAffector not found!");
            return;
        }
        
        // Use reflection to get private fields
        var sourceField = gravityAffector.GetType().GetField("currentGravitySource", 
            BindingFlags.NonPublic | BindingFlags.Instance);
        var currentSource = sourceField?.GetValue(gravityAffector);
        
        Vector3 gravityDir = gravityAffector.GetGravityDirection();
        
        Debug.Log($"Current gravity source: {currentSource}");
        Debug.Log($"Gravity direction: {gravityDir}");
        Debug.Log($"Gravity magnitude: {gravityDir.magnitude}");
        
        if (currentSource == null)
        {
            Debug.LogWarning("⚠️ No gravity source assigned!");
        }
        else if (currentSource is ShipGravity)
        {
            Debug.Log("✅ Using ship gravity");
        }
        else if (currentSource is CelestialBody)
        {
            Debug.Log("✅ Using planetary gravity");
        }
    }
    
    void DiagnoseGroundDetection()
    {
        Debug.Log("--- GROUND DETECTION ---");
        
        if (player == null) return;
        
        // Use reflection to get private isGrounded field
        var groundedField = player.GetType().GetField("isGrounded", 
            BindingFlags.NonPublic | BindingFlags.Instance);
        bool isGrounded = (bool)(groundedField?.GetValue(player) ?? false);
        
        Debug.Log($"Is grounded: {isGrounded}");
        Debug.Log($"Ground mask: {player.groundMask.value}");
        Debug.Log($"Ground check distance: {player.groundCheckDistance}");
        
        if (player.groundCheck != null)
        {
            Debug.Log($"Ground check position: {player.groundCheck.position}");
            
            // Test ground detection manually
            bool sphereCheck = Physics.CheckSphere(player.groundCheck.position, 
                player.groundCheckDistance, player.groundMask);
            Debug.Log($"Manual sphere check: {sphereCheck}");
        }
        else
        {
            Debug.LogError("❌ Ground check transform not assigned!");
        }
    }
    
    void DiagnoseInputSystem()
    {
        Debug.Log("--- INPUT SYSTEM ---");
        
        if (player == null) return;
        
        float mouseX = Input.GetAxisRaw("Mouse X");
        float mouseY = Input.GetAxisRaw("Mouse Y");
        
        Debug.Log($"Mouse input - X: {mouseX}, Y: {mouseY}");
        Debug.Log($"Cursor lock state: {Cursor.lockState}");
        Debug.Log($"Cursor visible: {Cursor.visible}");
        
        // Check input settings
        var inputSettingsField = player.GetType().GetField("inputSettings", 
            BindingFlags.Public | BindingFlags.Instance);
        var inputSettings = inputSettingsField?.GetValue(player);
        
        if (inputSettings != null)
        {
            var sensField = inputSettings.GetType().GetField("mouseSensitivity");
            var smoothField = inputSettings.GetType().GetField("mouseSmoothing");
            
            float sensitivity = (float)(sensField?.GetValue(inputSettings) ?? 0f);
            float smoothing = (float)(smoothField?.GetValue(inputSettings) ?? 0f);
            
            Debug.Log($"Mouse sensitivity: {sensitivity}");
            Debug.Log($"Mouse smoothing: {smoothing}");
        }
        else
        {
            Debug.LogWarning("⚠️ Input settings not found!");
        }
    }
    
    void DiagnoseShipGravity()
    {
        Debug.Log("--- SHIP GRAVITY ---");
        
        if (currentShip == null)
        {
            Debug.Log("No ship found in scene");
            return;
        }
        
        Vector3 shipGravityDir = currentShip.GetArtificialGravityDirection();
        Debug.Log($"Ship gravity direction: {shipGravityDir}");
        Debug.Log($"Ship transform.up: {currentShip.transform.up}");
        Debug.Log($"Ship rotation: {currentShip.transform.rotation.eulerAngles}");
        
        ShipGravity shipGravity = currentShip.GetComponentInChildren<ShipGravity>();
        if (shipGravity != null)
        {
            Debug.Log($"Ship gravity force: {shipGravity.GetGravityStrength(player.transform.position)}");
        }
    }
    
    void ApplyQuickFixes()
    {
        Debug.Log("=== APPLYING QUICK FIXES ===");
        
        if (player == null)
        {
            Debug.LogError("Player not found!");
            return;
        }
        
        // Fix 1: Reset cursor lock
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
        Debug.Log("✅ Fixed cursor lock");
        
        // Fix 2: Ensure input settings
        var inputSettingsField = player.GetType().GetField("inputSettings", 
            BindingFlags.Public | BindingFlags.Instance);
        var inputSettings = inputSettingsField?.GetValue(player);
        
        if (inputSettings == null)
        {
            var newSettings = ScriptableObject.CreateInstance<InputSettings>();
            newSettings.mouseSensitivity = 100f;
            newSettings.mouseSmoothing = 0.2f;
            inputSettingsField?.SetValue(player, newSettings);
            Debug.Log("✅ Created new input settings");
        }
        
        // Fix 3: Reset gravity if needed
        if (gravityAffector != null)
        {
            var sourceField = gravityAffector.GetType().GetField("currentGravitySource", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            var currentSource = sourceField?.GetValue(gravityAffector);
            
            if (currentSource == null)
            {
                gravityAffector.SwitchToPlanetaryGravity();
                Debug.Log("✅ Reset to planetary gravity");
            }
        }
        
        Debug.Log("=== FIXES APPLIED ===");
    }
    
    void ContinuousDebug()
    {
        if (player == null) return;
        
        // Show basic status on screen
        var groundedField = player.GetType().GetField("isGrounded", 
            BindingFlags.NonPublic | BindingFlags.Instance);
        bool isGrounded = (bool)(groundedField?.GetValue(player) ?? false);
        
        Vector3 velocity = player.GetComponent<Rigidbody>().linearVelocity;
        
        // This will show in the console continuously (can be moved to OnGUI for screen display)
        if (Time.frameCount % 60 == 0) // Every 60 frames
        {
            Debug.Log($"Player Status - Grounded: {isGrounded}, Velocity: {velocity.magnitude:F2}");
        }
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 120));
        GUILayout.Label($"Press {diagnosticKey} for full diagnostic");
        GUILayout.Label($"Press {fixKey} to apply quick fixes");
        GUILayout.Label("Press M while playing to debug mouse input");
        GUILayout.EndArea();
    }
}
