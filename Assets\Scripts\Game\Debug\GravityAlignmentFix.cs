using UnityEngine;

/// <summary>
/// Emergency fix for gravity alignment issues - forces correct player orientation
/// </summary>
public class GravityAlignmentFix : MonoBehaviour
{
    [Header("Fix Configuration")]
    [SerializeField] private bool autoFixOnUpdate = true;
    [SerializeField] private bool forceShipAlignment = true;
    [SerializeField] private float alignmentSpeed = 10f;

    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private KeyCode debugKey = KeyCode.F;

    private PlayerController player;
    private Ship ship;

    void Start()
    {
        player = FindFirstObjectByType<PlayerController>();
        ship = FindFirstObjectByType<Ship>();
    }

    void Update()
    {
        if (Input.GetKeyDown(debugKey))
        {
            DebugGravityState();
        }

        if (autoFixOnUpdate && forceShipAlignment)
        {
            ForceCorrectAlignment();
        }
    }

    void DebugGravityState()
    {
        if (player == null || ship == null) return;

        Debug.Log("=== GRAVITY ALIGNMENT DEBUG ===");

        // Check currentShip assignment
        var currentShipField = player.GetType().GetField("currentShip",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var currentShip = currentShipField?.GetValue(player);

        Debug.Log($"Player currentShip: {currentShip}");
        Debug.Log($"Ship reference: {ship}");

        // Check gravity directions
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        Vector3 shipUp = ship.transform.up;
        Vector3 playerUp = player.transform.up;

        Debug.Log($"Ship gravity direction: {shipGravityDir}");
        Debug.Log($"Ship transform.up: {shipUp}");
        Debug.Log($"Player transform.up: {playerUp}");

        // Check what player should be using
        Vector3 expectedPlayerUp = -shipGravityDir;
        Debug.Log($"Expected player up (-shipGravity): {expectedPlayerUp}");
        Debug.Log($"Expected matches ship.up: {Vector3.Dot(expectedPlayerUp, shipUp) > 0.99f}");

        // Check alignment
        float alignment = Vector3.Dot(playerUp, expectedPlayerUp);
        Debug.Log($"Player alignment with expected: {alignment}");

        Debug.Log("=== END DEBUG ===");
    }

    void ForceCorrectAlignment()
    {
        if (player == null || ship == null) return;

        // Check if player should be using ship gravity
        var currentShipField = player.GetType().GetField("currentShip",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var currentShip = currentShipField?.GetValue(player);

        if ((Object)currentShip != ship) return; // Player not in ship

        // Force correct orientation
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        Vector3 correctUp = -shipGravityDir; // Player up should be opposite of gravity

        // Check if player is misaligned
        float alignment = Vector3.Dot(player.transform.up, correctUp);

        if (alignment < 0.9f) // Player is not properly aligned
        {
            // Force correct orientation
            Vector3 playerForward = Vector3.ProjectOnPlane(player.transform.forward, correctUp);
            if (playerForward.sqrMagnitude < 0.001f)
                playerForward = Vector3.ProjectOnPlane(ship.transform.forward, correctUp);

            Quaternion targetRotation = Quaternion.LookRotation(playerForward, correctUp);
            player.transform.rotation = Quaternion.Slerp(player.transform.rotation, targetRotation,
                Time.deltaTime * alignmentSpeed);

            if (enableDebugLogs && Time.frameCount % 60 == 0) // Log every 60 frames
            {
                Debug.Log($"[GravityAlignmentFix] Forcing alignment - current: {alignment:F3}");
            }
        }
    }

    [ContextMenu("Force Immediate Alignment")]
    public void ForceImmediateAlignment()
    {
        if (player == null || ship == null) return;

        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        Vector3 correctUp = -shipGravityDir;

        Vector3 playerForward = Vector3.ProjectOnPlane(ship.transform.forward, correctUp);
        Quaternion targetRotation = Quaternion.LookRotation(playerForward, correctUp);

        player.transform.rotation = targetRotation;

        Debug.Log($"[GravityAlignmentFix] Forced immediate alignment to ship orientation");
        Debug.Log($"Ship gravity: {shipGravityDir}, Player up: {correctUp}");
    }

    [ContextMenu("Test Ship Assignment")]
    public void TestShipAssignment()
    {
        if (player == null || ship == null) return;

        Debug.Log("=== TESTING SHIP ASSIGNMENT ===");

        // Force ship assignment
        var currentShipField = player.GetType().GetField("currentShip",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        currentShipField?.SetValue(player, ship);

        // Force gravity switch
        var gravityAffector = player.GetComponent<GravityAffector>();
        if (gravityAffector != null)
        {
            var shipGravity = ship.GetComponentInChildren<ShipGravity>();
            if (shipGravity != null)
            {
                gravityAffector.SwitchToInteriorGravity(shipGravity);
                Debug.Log("Forced gravity switch to ship gravity");
            }
        }

        Debug.Log("=== SHIP ASSIGNMENT COMPLETE ===");
    }

    void OnGUI()
    {
        if (!enableDebugLogs) return;

        GUILayout.BeginArea(new Rect(10, 550, 400, 120));
        GUILayout.Label("Gravity Alignment Fix");
        GUILayout.Label($"Press {debugKey} to debug gravity state");
        GUILayout.Label($"Auto-fix enabled: {autoFixOnUpdate}");

        if (GUILayout.Button("Force Immediate Alignment"))
        {
            ForceImmediateAlignment();
        }

        if (GUILayout.Button("Test Ship Assignment"))
        {
            TestShipAssignment();
        }

        GUILayout.EndArea();
    }
}

