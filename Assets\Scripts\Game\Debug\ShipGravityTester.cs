using UnityEngine;

/// <summary>
/// Debug utility for testing ship gravity and movement functionality
/// </summary>
public class ShipGravityTester : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool enableDebugOutput = true;
    [SerializeField] private KeyCode testKey = KeyCode.T;
    
    private PlayerController player;
    private Ship currentShip;
    private GravityAffector gravityAffector;
    
    void Start()
    {
        player = FindObjectOfType<PlayerController>();
        currentShip = FindObjectOfType<Ship>();
        
        if (player != null)
        {
            gravityAffector = player.GetComponent<GravityAffector>();
        }
    }
    
    void Update()
    {
        if (Input.GetKeyDown(testKey))
        {
            RunGravityTest();
        }
        
        if (enableDebugOutput && Input.GetKeyDown(KeyCode.G))
        {
            LogCurrentGravityState();
        }
    }
    
    void RunGravityTest()
    {
        if (player == null || currentShip == null)
        {
            Debug.LogError("[ShipGravityTester] Player or Ship not found!");
            return;
        }
        
        Debug.Log("=== Ship Gravity Test Started ===");
        
        // Test 1: Check gravity source
        TestGravitySource();
        
        // Test 2: Check physics materials
        TestPhysicsMaterials();
        
        // Test 3: Check player movement capability
        TestPlayerMovement();
        
        Debug.Log("=== Ship Gravity Test Completed ===");
    }
    
    void TestGravitySource()
    {
        if (gravityAffector == null)
        {
            Debug.LogError("[Test] GravityAffector not found on player!");
            return;
        }
        
        Vector3 gravityDir = gravityAffector.GetGravityDirection();
        
        // Use reflection to get the current gravity source
        var field = gravityAffector.GetType().GetField("currentGravitySource", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var currentSource = field?.GetValue(gravityAffector);
        
        Debug.Log($"[Test] Current gravity source: {currentSource}");
        Debug.Log($"[Test] Gravity direction: {gravityDir}");
        
        if (currentSource is ShipGravity)
        {
            Debug.Log("[Test] ✓ Player is using ship gravity");
        }
        else if (currentSource is CelestialBody)
        {
            Debug.Log("[Test] ✓ Player is using planetary gravity");
        }
        else
        {
            Debug.LogWarning("[Test] ⚠ Unknown gravity source type");
        }
    }
    
    void TestPhysicsMaterials()
    {
        if (currentShip == null)
        {
            Debug.LogError("[Test] Ship not found!");
            return;
        }
        
        Collider[] shipColliders = currentShip.GetComponentsInChildren<Collider>();
        int interiorCount = 0;
        int exteriorCount = 0;
        int noMaterialCount = 0;
        
        foreach (Collider col in shipColliders)
        {
            if (col.isTrigger) continue;
            
            if (col.material == null)
            {
                noMaterialCount++;
                Debug.LogWarning($"[Test] Collider '{col.name}' has no physics material assigned");
            }
            else if (col.material.dynamicFriction < 1.0f)
            {
                interiorCount++;
                Debug.Log($"[Test] Interior material on '{col.name}' (friction: {col.material.dynamicFriction})");
            }
            else
            {
                exteriorCount++;
                Debug.Log($"[Test] Exterior material on '{col.name}' (friction: {col.material.dynamicFriction})");
            }
        }
        
        Debug.Log($"[Test] Physics materials summary: {interiorCount} interior, {exteriorCount} exterior, {noMaterialCount} unassigned");
        
        if (interiorCount > 0)
        {
            Debug.Log("[Test] ✓ Interior surfaces have low friction materials");
        }
        else
        {
            Debug.LogWarning("[Test] ⚠ No interior surfaces found with low friction materials");
        }
    }
    
    void TestPlayerMovement()
    {
        if (player == null)
        {
            Debug.LogError("[Test] Player not found!");
            return;
        }
        
        Rigidbody playerRb = player.GetComponent<Rigidbody>();
        if (playerRb == null)
        {
            Debug.LogError("[Test] Player rigidbody not found!");
            return;
        }
        
        Vector3 velocity = playerRb.linearVelocity;
        bool isGrounded = player.GetType().GetField("isGrounded", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.GetValue(player) as bool? ?? false;
        
        Debug.Log($"[Test] Player velocity: {velocity}");
        Debug.Log($"[Test] Player grounded: {isGrounded}");
        Debug.Log($"[Test] Player position: {player.transform.position}");
        
        // Check if player is inside ship gravity trigger
        ShipGravity shipGravity = currentShip.GetComponentInChildren<ShipGravity>();
        if (shipGravity != null)
        {
            Collider gravityTrigger = shipGravity.GetComponent<Collider>();
            if (gravityTrigger != null && gravityTrigger.bounds.Contains(player.transform.position))
            {
                Debug.Log("[Test] ✓ Player is inside ship gravity trigger");
            }
            else
            {
                Debug.Log("[Test] ✓ Player is outside ship gravity trigger");
            }
        }
    }
    
    void LogCurrentGravityState()
    {
        if (player == null || gravityAffector == null) return;
        
        Vector3 gravityDir = gravityAffector.GetGravityDirection();
        var field = gravityAffector.GetType().GetField("currentGravitySource", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var currentSource = field?.GetValue(gravityAffector);
        
        Debug.Log($"[Gravity] Source: {currentSource}, Direction: {gravityDir}");
    }
    
    void OnGUI()
    {
        if (!enableDebugOutput) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 100));
        GUILayout.Label($"Press {testKey} to run ship gravity test");
        GUILayout.Label("Press G to log current gravity state");
        GUILayout.EndArea();
    }
}
