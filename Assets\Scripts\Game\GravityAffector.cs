using UnityEngine;

public interface IGravitySource
{
    Vector3 GetGravityDirection(Vector3 position);
    float GetGravityStrength(Vector3 position);
    Vector3 GetVelocity();
}

[RequireComponent(typeof(Rigidbody))]
public class GravityAffector : MonoBehaviour
{
    [SerializeField] private float gravitySmoothTime = 0.3f;
    [SerializeField] private float maxGravityStrength = 100f;
    [SerializeField] private float initialGravitySearchRadius = 1000f;

    private Rigidbody rb;
    private IGravitySource currentGravitySource;
    private Vector3 lastGravityDirection;
    private Vector3 gravityVelocity;

    private void Awake()
    {
        rb = GetComponent<Rigidbody>();
        rb.useGravity = false;
        lastGravityDirection = Vector3.down;
    }

    private void Start() => FindInitialGravitySource();

    private void FixedUpdate()
    {
        if (currentGravitySource == null) return;

        Vector3 dir = currentGravitySource.GetGravityDirection(transform.position);
        float g = currentGravitySource.GetGravityStrength(transform.position);

        // validation / clamping
        if (float.IsNaN(g) || float.IsInfinity(g) || g > maxGravityStrength) g = maxGravityStrength;
        if (dir == Vector3.zero) dir = lastGravityDirection;

        // smooth direction changes
        lastGravityDirection = Vector3.SmoothDamp(lastGravityDirection, dir, ref gravityVelocity, gravitySmoothTime);

        rb.AddForce(lastGravityDirection * g, ForceMode.Acceleration);
    }

    public void SwitchToInteriorGravity(IGravitySource interiorGravity)
    {
        currentGravitySource = interiorGravity;
        lastGravityDirection = interiorGravity.GetGravityDirection(transform.position);
    }

    public void SwitchToPlanetaryGravity()
    {
        currentGravitySource = null;
        FindInitialGravitySource();
    }

    public Vector3 GetGravityDirection() => lastGravityDirection;

    private void FindInitialGravitySource()
    {
        CelestialBody[] bodies = FindObjectsByType<CelestialBody>(FindObjectsSortMode.None);
        float closest = float.MaxValue;
        CelestialBody best = null;

        foreach (CelestialBody body in bodies)
        {
            float d = Vector3.Distance(transform.position, body.transform.position);
            if (d < closest && d < initialGravitySearchRadius)
            {
                closest = d;
                best = body;
            }
        }

        if (best != null)
        {
            currentGravitySource = best;
        }
    }
}
