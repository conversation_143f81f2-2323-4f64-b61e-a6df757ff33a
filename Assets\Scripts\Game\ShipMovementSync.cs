using UnityEngine;

/// <summary>
/// Advanced ship movement synchronization system with multiple approaches
/// </summary>
public class ShipMovementSync : MonoBehaviour
{
    [Header("Sync Method")]
    [SerializeField] private SyncMethod syncMethod = SyncMethod.VelocityInheritance;
    
    [Header("Velocity Inheritance Settings")]
    [SerializeField] private bool enableVelocityInheritance = true;
    [SerializeField] private float velocityBlendFactor = 1f;
    [SerializeField] private bool preservePlayerMovement = true;
    
    [Header("Parenting Settings")]
    [SerializeField] private bool enableParenting = false;
    [SerializeField] private bool unparentOnExit = true;
    
    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = false;
    [SerializeField] private bool showVelocityGizmos = false;
    
    public enum SyncMethod
    {
        VelocityInheritance,  // Continuously inherit ship velocity (recommended)
        Parenting,            // Parent player to ship (simple but can cause issues)
        Hybrid                // Combination of both approaches
    }
    
    private PlayerController player;
    private Ship ship;
    private Rigidbody playerRb;
    private Rigidbody shipRb;
    private Vector3 lastShipVelocity;
    private Vector3 lastPlayerLocalVelocity;
    private Transform originalParent;
    private bool isPlayerInShip = false;
    
    void Start()
    {
        player = FindObjectOfType<PlayerController>();
        ship = FindObjectOfType<Ship>();
        
        if (player != null)
        {
            playerRb = player.GetComponent<Rigidbody>();
            originalParent = player.transform.parent;
        }
        
        if (ship != null)
        {
            shipRb = ship.GetComponent<Rigidbody>();
        }
    }
    
    void FixedUpdate()
    {
        if (!isPlayerInShip || player == null || ship == null) return;
        
        switch (syncMethod)
        {
            case SyncMethod.VelocityInheritance:
                HandleVelocityInheritance();
                break;
            case SyncMethod.Parenting:
                HandleParenting();
                break;
            case SyncMethod.Hybrid:
                HandleHybridApproach();
                break;
        }
    }
    
    void HandleVelocityInheritance()
    {
        if (!enableVelocityInheritance || shipRb == null || playerRb == null) return;
        
        Vector3 currentShipVelocity = shipRb.linearVelocity;
        Vector3 shipVelocityDelta = currentShipVelocity - lastShipVelocity;
        
        if (preservePlayerMovement)
        {
            // Get player's movement relative to ship
            Vector3 playerWorldVelocity = playerRb.linearVelocity;
            Vector3 playerRelativeVelocity = playerWorldVelocity - lastShipVelocity;
            
            // Apply ship velocity change while preserving player movement
            Vector3 newVelocity = playerRelativeVelocity + currentShipVelocity;
            playerRb.linearVelocity = Vector3.Lerp(playerRb.linearVelocity, newVelocity, velocityBlendFactor);
        }
        else
        {
            // Simple approach: just add ship velocity delta
            playerRb.linearVelocity += shipVelocityDelta * velocityBlendFactor;
        }
        
        lastShipVelocity = currentShipVelocity;
        
        if (enableDebugLogs && Time.fixedTime % 1f < Time.fixedDeltaTime)
        {
            Debug.Log($"[ShipMovementSync] Ship velocity: {currentShipVelocity}, Player velocity: {playerRb.linearVelocity}");
        }
    }
    
    void HandleParenting()
    {
        if (!enableParenting) return;
        
        // Parenting is handled in OnPlayerEnterShip/OnPlayerExitShip
        // This method can be used for additional parenting-specific logic
    }
    
    void HandleHybridApproach()
    {
        // Use parenting for position/rotation sync and velocity inheritance for smooth movement
        HandleVelocityInheritance();
        
        // Additional hybrid logic can be added here
    }
    
    public void OnPlayerEnterShip()
    {
        if (player == null || ship == null) return;
        
        isPlayerInShip = true;
        
        if (shipRb != null)
        {
            lastShipVelocity = shipRb.linearVelocity;
        }
        
        switch (syncMethod)
        {
            case SyncMethod.Parenting:
            case SyncMethod.Hybrid:
                if (enableParenting)
                {
                    originalParent = player.transform.parent;
                    player.transform.SetParent(ship.transform);
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log("[ShipMovementSync] Player parented to ship");
                    }
                }
                break;
        }
        
        if (enableDebugLogs)
        {
            Debug.Log($"[ShipMovementSync] Player entered ship using {syncMethod} method");
        }
    }
    
    public void OnPlayerExitShip()
    {
        if (player == null) return;
        
        isPlayerInShip = false;
        
        switch (syncMethod)
        {
            case SyncMethod.Parenting:
            case SyncMethod.Hybrid:
                if (enableParenting && unparentOnExit)
                {
                    player.transform.SetParent(originalParent);
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log("[ShipMovementSync] Player unparented from ship");
                    }
                }
                break;
        }
        
        lastShipVelocity = Vector3.zero;
        
        if (enableDebugLogs)
        {
            Debug.Log("[ShipMovementSync] Player exited ship");
        }
    }
    
    [ContextMenu("Test Ship Entry")]
    public void TestShipEntry()
    {
        OnPlayerEnterShip();
    }
    
    [ContextMenu("Test Ship Exit")]
    public void TestShipExit()
    {
        OnPlayerExitShip();
    }
    
    [ContextMenu("Switch to Velocity Inheritance")]
    public void SwitchToVelocityInheritance()
    {
        syncMethod = SyncMethod.VelocityInheritance;
        enableVelocityInheritance = true;
        enableParenting = false;
        Debug.Log("[ShipMovementSync] Switched to Velocity Inheritance mode");
    }
    
    [ContextMenu("Switch to Parenting")]
    public void SwitchToParenting()
    {
        syncMethod = SyncMethod.Parenting;
        enableVelocityInheritance = false;
        enableParenting = true;
        Debug.Log("[ShipMovementSync] Switched to Parenting mode");
    }
    
    void OnDrawGizmos()
    {
        if (!showVelocityGizmos || !isPlayerInShip) return;
        
        if (player != null && playerRb != null)
        {
            // Draw player velocity
            Gizmos.color = Color.green;
            Gizmos.DrawRay(player.transform.position, playerRb.linearVelocity);
        }
        
        if (ship != null && shipRb != null)
        {
            // Draw ship velocity
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(ship.transform.position, shipRb.linearVelocity);
        }
    }
}
