using UnityEngine;

/// <summary>
/// Fix for ship gravity direction inversion issue
/// Tests and corrects the gravity direction to ensure proper player orientation
/// </summary>
public class ShipGravityDirectionFix : MonoBehaviour
{
    [Header("Fix Options")]
    [SerializeField] private bool autoFixOnStart = true;
    [SerializeField] private bool invertShipGravity = false;
    [SerializeField] private bool useTransformUpDirectly = true;
    
    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool showGizmos = true;
    
    private Ship ship;
    private ShipGravity shipGravity;
    
    void Start()
    {
        ship = GetComponent<Ship>();
        shipGravity = GetComponentInChildren<ShipGravity>();
        
        if (autoFixOnStart)
        {
            AnalyzeAndFixGravityDirection();
        }
    }
    
    [ContextMenu("Analyze Gravity Direction")]
    public void AnalyzeAndFixGravityDirection()
    {
        if (ship == null || shipGravity == null)
        {
            Debug.LogError("[ShipGravityDirectionFix] Ship or ShipGravity component not found!");
            return;
        }
        
        if (enableDebugLogs)
        {
            Debug.Log("=== SHIP GRAVITY DIRECTION ANALYSIS ===");
            
            Vector3 shipUp = ship.transform.up;
            Vector3 shipDown = -ship.transform.up;
            Vector3 currentGravityDir = ship.GetArtificialGravityDirection();
            
            Debug.Log($"Ship transform.up (ceiling): {shipUp}");
            Debug.Log($"Ship transform.down (floor): {shipDown}");
            Debug.Log($"Current gravity direction: {currentGravityDir}");
            
            // Check if gravity points toward floor or ceiling
            float dotWithDown = Vector3.Dot(currentGravityDir, shipDown);
            float dotWithUp = Vector3.Dot(currentGravityDir, shipUp);
            
            Debug.Log($"Gravity dot with floor direction: {dotWithDown}");
            Debug.Log($"Gravity dot with ceiling direction: {dotWithUp}");
            
            if (dotWithDown > 0.9f)
            {
                Debug.Log("✅ Gravity correctly points toward floor");
            }
            else if (dotWithUp > 0.9f)
            {
                Debug.Log("❌ Gravity incorrectly points toward ceiling - NEEDS INVERSION");
                invertShipGravity = true;
            }
            else
            {
                Debug.LogWarning("⚠️ Gravity direction is ambiguous");
            }
            
            Debug.Log("=== END ANALYSIS ===");
        }
        
        // Apply fix if needed
        if (invertShipGravity)
        {
            ApplyGravityDirectionFix();
        }
    }
    
    void ApplyGravityDirectionFix()
    {
        if (enableDebugLogs)
        {
            Debug.Log("[ShipGravityDirectionFix] Applying gravity direction fix...");
        }
        
        // We'll modify the Ship's GetArtificialGravityDirection method behavior
        // by overriding it in this component
    }
    
    /// <summary>
    /// Get the corrected artificial gravity direction
    /// </summary>
    public Vector3 GetCorrectedGravityDirection()
    {
        if (ship == null) return Vector3.down;
        
        if (useTransformUpDirectly)
        {
            // Use ship's down direction directly (toward floor)
            return -ship.transform.up;
        }
        else if (invertShipGravity)
        {
            // Invert the ship's gravity direction
            return -ship.GetArtificialGravityDirection();
        }
        else
        {
            // Use ship's gravity direction as-is
            return ship.GetArtificialGravityDirection();
        }
    }
    
    /// <summary>
    /// Get the corrected "up" direction for player orientation
    /// </summary>
    public Vector3 GetCorrectedUpDirection()
    {
        if (ship == null) return Vector3.up;
        
        // Player's up should always be toward ship's ceiling
        return ship.transform.up;
    }
    
    /// <summary>
    /// Test both gravity direction interpretations
    /// </summary>
    [ContextMenu("Test Both Interpretations")]
    public void TestBothInterpretations()
    {
        if (ship == null) return;
        
        Debug.Log("=== TESTING BOTH GRAVITY INTERPRETATIONS ===");
        
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        Vector3 shipTransformUp = ship.transform.up;
        Vector3 shipTransformDown = -ship.transform.up;
        
        Debug.Log($"Ship gravity direction: {shipGravityDir}");
        Debug.Log($"Ship transform.up: {shipTransformUp}");
        Debug.Log($"Ship transform.down: {shipTransformDown}");
        
        // Test interpretation 1: Use -gravityDir as player up
        Vector3 playerUp1 = -shipGravityDir;
        Debug.Log($"Interpretation 1 - Player up (-gravityDir): {playerUp1}");
        Debug.Log($"  Matches ship ceiling: {Vector3.Dot(playerUp1, shipTransformUp) > 0.9f}");
        
        // Test interpretation 2: Use transform.up as player up
        Vector3 playerUp2 = shipTransformUp;
        Debug.Log($"Interpretation 2 - Player up (transform.up): {playerUp2}");
        Debug.Log($"  Matches ship ceiling: {Vector3.Dot(playerUp2, shipTransformUp) > 0.9f}");
        
        // Recommendation
        if (Vector3.Dot(playerUp1, shipTransformUp) > 0.9f)
        {
            Debug.Log("✅ RECOMMENDATION: Use -gravityDir as player up");
            useTransformUpDirectly = false;
        }
        else if (Vector3.Dot(playerUp2, shipTransformUp) > 0.9f)
        {
            Debug.Log("✅ RECOMMENDATION: Use transform.up as player up");
            useTransformUpDirectly = true;
        }
        else
        {
            Debug.LogWarning("⚠️ Neither interpretation aligns with ship ceiling!");
        }
        
        Debug.Log("=== END INTERPRETATION TEST ===");
    }
    
    void OnDrawGizmos()
    {
        if (!showGizmos || ship == null) return;
        
        Vector3 shipPos = ship.transform.position;
        
        // Draw ship's transform axes
        Gizmos.color = Color.red;
        Gizmos.DrawRay(shipPos, ship.transform.right * 2f);
        
        Gizmos.color = Color.green;
        Gizmos.DrawRay(shipPos, ship.transform.up * 2f);
        
        Gizmos.color = Color.blue;
        Gizmos.DrawRay(shipPos, ship.transform.forward * 2f);
        
        // Draw gravity direction
        Vector3 gravityDir = ship.GetArtificialGravityDirection();
        Gizmos.color = Color.yellow;
        Gizmos.DrawRay(shipPos, gravityDir * 3f);
        Gizmos.DrawWireSphere(shipPos + gravityDir * 3f, 0.2f);
        
        // Draw corrected directions
        Vector3 correctedGravity = GetCorrectedGravityDirection();
        Vector3 correctedUp = GetCorrectedUpDirection();
        
        Gizmos.color = Color.magenta;
        Gizmos.DrawRay(shipPos + Vector3.right * 0.5f, correctedGravity * 3f);
        
        Gizmos.color = Color.cyan;
        Gizmos.DrawRay(shipPos + Vector3.right * 0.5f, correctedUp * 3f);
    }
    
    void OnGUI()
    {
        if (!enableDebugLogs) return;
        
        GUILayout.BeginArea(new Rect(10, 300, 400, 150));
        GUILayout.Label("Ship Gravity Direction Fix");
        GUILayout.Label($"Invert Gravity: {invertShipGravity}");
        GUILayout.Label($"Use Transform Up: {useTransformUpDirectly}");
        
        if (GUILayout.Button("Analyze Gravity Direction"))
        {
            AnalyzeAndFixGravityDirection();
        }
        
        if (GUILayout.Button("Test Both Interpretations"))
        {
            TestBothInterpretations();
        }
        
        GUILayout.EndArea();
    }
}
