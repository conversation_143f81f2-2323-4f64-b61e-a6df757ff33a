# Ship Gravity Movement Fix - Complete Solution

## Problem Description

After implementing the physics material fix for ship interior friction, several interconnected issues emerged:

1. **Gravity and Jump Problems** - Player movement no longer sticks to surfaces, jump functionality broken
2. **Mouse Camera Control** - Mouse input for camera rotation not working properly
3. **Ship Rotation Physics** - Player doesn't maintain proper orientation when exiting pilot mode after ship rotation

## Root Causes Identified

### Original Issue

The `Spaceship.physicMaterial` had extremely high friction values (2000) that prevented player movement inside the ship.

### Secondary Issues After Fix

1. **Ground Detection**: Layer mask or ground check configuration issues
2. **Input System**: InputSettings not properly initialized or cursor lock issues
3. **Ship Gravity Orientation**: Player positioning after ship rotation doesn't account for new gravity direction

## Solution Overview

The solution implements a dual physics material system:

1. **Exterior Material** (`Spaceship.physicMaterial`): High friction (2000) for ship landing stability
2. **Interior Material** (`ShipInterior.physicMaterial`): Low friction (0.3-0.4) for smooth player movement

## Complete Solution Files

### New Files Created:

- `Assets\Prefabs\ShipInterior.physicMaterial` - Low friction material for interior surfaces
- `Assets\Scripts\Game\ShipPhysicsMaterialManager.cs` - Standalone material management component
- `Assets\Scripts\Game\Editor\ShipPhysicsDebugger.cs` - Editor tool for debugging physics materials
- `Assets\Scripts\Game\QuickShipFix.cs` - Runtime friction fix
- `Assets\Scripts\Game\Debug\PlayerSystemDiagnostic.cs` - Comprehensive diagnostic tool
- `Assets\Scripts\Game\ComprehensivePlayerFix.cs` - Complete fix for all issues

### Modified Files:

- `Assets\Scripts\Game\Controllers\Ship.cs` - Added physics material management and improved ship exit logic
- `Assets\Scripts\Game\Controllers\PlayerController.cs` - Improved input initialization and ground detection

## Implementation Steps

### 1. Automatic Setup (Recommended)

The Ship class now automatically assigns physics materials on startup:

1. **Interior surfaces** (detected by name keywords or position within ship gravity trigger) get low friction material
2. **Exterior surfaces** get high friction material for landing stability

### 2. Manual Setup (If Needed)

If automatic detection doesn't work perfectly for your ship model:

1. Open the Ship Physics Debugger: `Tools > Ship Physics Debugger`
2. Select your ship in the scene
3. Click "List All Colliders" to see current material assignments
4. Manually assign materials to specific colliders if needed

### 3. Ship Model Requirements

For best results, name your ship interior colliders with keywords like:

- "Interior"
- "Floor"
- "Wall"
- "Ramp"
- "Ceiling"
- "Walkway"
- "Cabin"
- "Deck"

## Physics Material Settings

### Interior Material (`ShipInterior.physicMaterial`):

```
dynamicFriction: 0.3
staticFriction: 0.4
bounciness: 0
frictionCombine: 0 (Average)
bounceCombine: 0 (Average)
```

### Exterior Material (`Spaceship.physicMaterial`):

```
dynamicFriction: 2000
staticFriction: 2000
bounciness: 0
frictionCombine: 3 (Maximum)
bounceCombine: 1 (Average)
```

## Testing the Fix

1. **Enter the ship** - Gravity should switch to ship gravity (pointing toward ship floor)
2. **Walk around interior** - Player should move smoothly on floors and ramps
3. **Test ramps** - Player should be able to walk up/down ramps without getting stuck
4. **Exit the ship** - Gravity should switch back to planetary gravity

## Debugging Tools

### Ship Physics Debugger Window

- Access via `Tools > Ship Physics Debugger`
- Shows all colliders and their current physics materials
- Allows manual material assignment testing

### Console Logging

The Ship class logs material assignment information:

```
[Ship] Assigned materials to X exterior and Y interior colliders
```

### In-Game Debug

Use the existing gravity debug UI (Tab key) to verify gravity source switching.

## Troubleshooting

### Player Still Can't Move in Ship

1. Check that interior colliders have the low friction material assigned
2. Verify ship gravity is working (check gravity debug UI)
3. Ensure colliders are not marked as triggers when they should be solid

### Ship Slides on Planet Surface

1. Verify exterior/landing colliders have high friction material
2. Check that the ship's main body colliders are using exterior material

### Materials Not Assigning Automatically

1. Check collider naming conventions
2. Verify ShipGravity component is present and configured
3. Use the Ship Physics Debugger to manually assign materials

## Performance Notes

- Material assignment happens once during ship startup
- No runtime performance impact
- Automatic detection uses simple string matching and bounds checking

## Future Improvements

- Add support for custom material assignment rules
- Implement material blending for transition areas
- Add visual indicators in editor for material assignments
