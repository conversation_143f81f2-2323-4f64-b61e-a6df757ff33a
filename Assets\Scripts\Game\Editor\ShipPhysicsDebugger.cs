using UnityEngine;
using UnityEditor;

/// <summary>
/// Editor utility for debugging ship physics materials and collision issues
/// </summary>
public class ShipPhysicsDebugger : EditorWindow
{
    private Ship selectedShip;
    private Vector2 scrollPosition;
    
    [MenuItem("Tools/Ship Physics Debugger")]
    public static void ShowWindow()
    {
        GetWindow<ShipPhysicsDebugger>("Ship Physics Debugger");
    }
    
    void OnGUI()
    {
        GUILayout.Label("Ship Physics Material Debugger", EditorStyles.boldLabel);
        
        // Ship selection
        selectedShip = (Ship)EditorGUILayout.ObjectField("Ship", selectedShip, typeof(Ship), true);
        
        if (selectedShip == null)
        {
            EditorGUILayout.HelpBox("Select a Ship to debug physics materials", MessageType.Info);
            return;
        }
        
        EditorGUILayout.Space();
        
        // Action buttons
        if (GUILayout.Button("Assign Physics Materials"))
        {
            selectedShip.SendMessage("AssignPhysicsMaterials");
        }
        
        if (GUILayout.Button("List All Colliders"))
        {
            ListAllColliders();
        }
        
        EditorGUILayout.Space();
        
        // Collider information
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        Collider[] colliders = selectedShip.GetComponentsInChildren<Collider>();
        
        EditorGUILayout.LabelField($"Total Colliders: {colliders.Length}", EditorStyles.boldLabel);
        
        foreach (Collider col in colliders)
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.LabelField($"Name: {col.name}");
            EditorGUILayout.LabelField($"Type: {col.GetType().Name}");
            EditorGUILayout.LabelField($"Is Trigger: {col.isTrigger}");
            
            if (col.material != null)
            {
                EditorGUILayout.LabelField($"Material: {col.material.name}");
                EditorGUILayout.LabelField($"Dynamic Friction: {col.material.dynamicFriction}");
                EditorGUILayout.LabelField($"Static Friction: {col.material.staticFriction}");
            }
            else
            {
                EditorGUILayout.LabelField("Material: None (using default)");
            }
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }
        
        EditorGUILayout.EndScrollView();
    }
    
    void ListAllColliders()
    {
        if (selectedShip == null) return;
        
        Collider[] colliders = selectedShip.GetComponentsInChildren<Collider>();
        
        Debug.Log($"=== Ship Colliders Debug Info for {selectedShip.name} ===");
        
        foreach (Collider col in colliders)
        {
            string materialInfo = col.material != null 
                ? $"{col.material.name} (Dynamic: {col.material.dynamicFriction}, Static: {col.material.staticFriction})"
                : "Default Material";
                
            Debug.Log($"Collider: {col.name} | Type: {col.GetType().Name} | Trigger: {col.isTrigger} | Material: {materialInfo}");
        }
        
        Debug.Log("=== End Ship Colliders Debug Info ===");
    }
}
