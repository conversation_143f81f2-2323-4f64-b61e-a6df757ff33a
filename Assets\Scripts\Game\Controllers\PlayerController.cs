// ───────────────────────────────────────
// PlayerController.cs  • final version
// ───────────────────────────────────────
using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
public class PlayerController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
{
    /* ── Movement ─────────────────────────────────────────────── */
    [Header("Movement settings")]
    public float walkSpeed = 8f;
    public float runSpeed = 14f;
    public float jumpForce = 20f;
    public float vSmoothTime = 0.10f;
    public float airSmoothTime = 0.50f;
    public float stickToGroundForce = 8f;
    public float groundCheckDistance = 0.10f;
    public float slopeLimit = 45f;
    public float slideSpeed = 5f;
    public float airControl = 0.10f;

    /* ── Jet-pack ─────────────────────────────────────────────── */
    [Head<PERSON>("Jet-pack settings")]
    public float jetpackForce = 10f;
    public float jetpackDuration = 2f;
    public float jetpackRefuelTime = 2f;
    public float jetpackRefuelDelay = 2f;

    /* ── Mouse ────────────────────────────────────────────────── */
    [Header("Mouse settings")]
    public float mouseSensitivityMultiplier = 1f;
    public float maxMouseSmoothTime = 0.3f;
    public Vector2 pitchMinMax = new(-40, 85);
    public InputSettings inputSettings;

    /* ── References ───────────────────────────────────────────── */
    [Header("References")]
    public Transform cameraTransform;   // main camera
    public Transform groundCheck;       // sphere-cast origin
    public Transform modelTransform;    // purely visual (optional)
    public LayerMask groundMask;

    /* ── Private state ────────────────────────────────────────── */
    Rigidbody rb;
    GravityAffector gravityAffector;
    Ship currentShip;
    Vector3 lastShipVelocity;

    bool isGrounded;
    bool usingJetpack;
    float jetpackFuelPercent = 1f;
    float lastJetpackUseTime;

    bool cursorLocked = true;

    // camera rotation
    float yaw, pitch, smoothYaw, smoothPitch;
    float yawSmoothV, pitchSmoothV, previousSmoothYaw;
    Vector3 smoothVelRef;

    public Vector3 CameraInitialLocalPos { get; private set; }

    /* ─────────────────────────────────────────────────────────── */
    #region Unity lifecycle
    void Awake()
    {
        rb = GetComponent<Rigidbody>();
        rb.useGravity = false;
        rb.freezeRotation = true;                       // physics never touches rotation
        rb.interpolation = RigidbodyInterpolation.None;
        rb.mass = 70f;

        gravityAffector = GetComponent<GravityAffector>() ??
                          gameObject.AddComponent<GravityAffector>();

        // fallback assignments
        cameraTransform ??= GetComponentInChildren<Camera>()?.transform;
        groundCheck ??= transform.Find("Feet");
        modelTransform ??= transform.Find("Model");

        if (cameraTransform == null)
        {
            Debug.LogError("PlayerController: no camera found.");
            enabled = false;
            return;
        }

        CameraInitialLocalPos = cameraTransform.localPosition;

        if (inputSettings == null)
        {
            inputSettings = ScriptableObject.CreateInstance<InputSettings>();
            inputSettings.mouseSensitivity = 100f;
            inputSettings.mouseSmoothing = 0.2f;
        }
        inputSettings.Begin();

        yaw = transform.eulerAngles.y;
        pitch = cameraTransform.localEulerAngles.x;
        smoothYaw = yaw;
        smoothPitch = pitch;
        previousSmoothYaw = smoothYaw;

        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    void Update()
    {
        ReadInput();
        RotateCamera();
        UpdateJetpackFuel();
    }

    void FixedUpdate()
    {
        Vector3 gDir = currentShip
                     ? currentShip.GetArtificialGravityDirection()
                     : gravityAffector.GetGravityDirection();

        if (currentShip != null && !IsShipMovementSyncActive())
            InheritShipVelocity();

        AlignToGravity(gDir);
        CheckGround(gDir);
        MoveCharacter(gDir);
    }
    #endregion

    /* ── Input / camera ───────────────────────────────────────── */

    void ReadInput()
    {
        if (Input.GetKeyDown(KeyCode.Space) && isGrounded)
            Jump();

        usingJetpack = Input.GetKey(KeyCode.Space) &&
                       !isGrounded &&
                       jetpackFuelPercent > 0f;

        if (Input.GetKeyDown(KeyCode.Escape))
        {
            cursorLocked = !cursorLocked;
            Cursor.lockState = cursorLocked ? CursorLockMode.Locked
                                            : CursorLockMode.None;
            Cursor.visible = !cursorLocked;
        }

        // Manual orientation reset for planetary gravity (press R key)
        if (Input.GetKeyDown(KeyCode.R) && currentShip == null)
        {
            ResetOrientationToPlanet();
        }
    }

    void RotateCamera()
    {
        if (!cursorLocked)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            cursorLocked = true;
            return;
        }

        float mx = Input.GetAxis("Mouse X");
        float my = Input.GetAxis("Mouse Y");

        float sens = inputSettings.mouseSensitivity * mouseSensitivityMultiplier;

        yaw += mx * sens * Time.deltaTime;
        pitch -= my * sens * Time.deltaTime;
        pitch = Mathf.Clamp(pitch, pitchMinMax.x, pitchMinMax.y);

        float smTime = Mathf.Lerp(0.01f, maxMouseSmoothTime, inputSettings.mouseSmoothing);
        smoothPitch = Mathf.SmoothDampAngle(smoothPitch, pitch, ref pitchSmoothV, smTime);
        smoothYaw = Mathf.SmoothDampAngle(smoothYaw, yaw, ref yawSmoothV, smTime);

        cameraTransform.localEulerAngles = Vector3.right * smoothPitch;

        float yawDelta = Mathf.DeltaAngle(previousSmoothYaw, smoothYaw);
        previousSmoothYaw = smoothYaw;

        if (Mathf.Abs(yawDelta) < 0.001f) return;

        // rotate around current up (already aligned to gravity or ship)
        transform.Rotate(transform.up, yawDelta, Space.World);
    }

    /* ── Character movement ───────────────────────────────────── */

    void MoveCharacter(Vector3 gDir)
    {
        float h = Input.GetAxisRaw("Horizontal");
        float v = Input.GetAxisRaw("Vertical");
        bool run = Input.GetKey(KeyCode.LeftShift);

        Vector3 input = new(h, 0f, v);
        if (input.sqrMagnitude > 1f) input.Normalize();

        float speed = run ? runSpeed : walkSpeed;
        Vector3 targetVel = transform.TransformDirection(input) * speed;
        targetVel = Vector3.ProjectOnPlane(targetVel, gDir);

        bool isParentedToShip = transform.parent &&
                                transform.parent.GetComponent<Ship>() &&
                                IsShipMovementSyncActive();

        if (isParentedToShip)
        {
            // let ShipMovementSync carry the base velocity; just add local input
            Vector3 currentVelocity = rb.linearVelocity;
            Vector3 lateral = Vector3.ProjectOnPlane(currentVelocity, gDir);
            Vector3 vertical = Vector3.Project(currentVelocity, gDir);

            Vector3 relativeMovement = targetVel * 0.3f;
            lateral += relativeMovement;

            rb.linearVelocity = lateral + vertical;
        }
        else
        {
            Vector3 lateral = Vector3.ProjectOnPlane(rb.linearVelocity, gDir);
            Vector3 vertical = Vector3.Project(rb.linearVelocity, gDir);

            float smooth = isGrounded ? vSmoothTime : airSmoothTime;
            lateral = Vector3.SmoothDamp(lateral, targetVel, ref smoothVelRef, smooth);

            rb.linearVelocity = lateral + vertical;
        }

        if (usingJetpack)
            rb.AddForce(-gDir * jetpackForce, ForceMode.Acceleration);

        if (isGrounded)
            rb.AddForce(-gDir * stickToGroundForce, ForceMode.Acceleration);
    }

    void CheckGround(Vector3 gDir)
    {
        if (groundCheck == null) { isGrounded = false; return; }

        float checkDist = groundCheckDistance * 1.2f;
        isGrounded = Physics.CheckSphere(groundCheck.position, checkDist, groundMask);

        if (!isGrounded)
            isGrounded = Physics.Raycast(groundCheck.position, gDir, checkDist * 2f, groundMask);

        if (!isGrounded) return;

        if (Physics.Raycast(groundCheck.position, gDir, out var hit, checkDist * 2f, groundMask))
        {
            float angle = Vector3.Angle(hit.normal, -gDir);
            if (angle > slopeLimit)
            {
                Vector3 slideDir = Vector3.ProjectOnPlane(gDir, hit.normal).normalized;
                rb.linearVelocity += slideDir * slideSpeed * Time.deltaTime;
            }
        }
    }

    void Jump()
    {
        Vector3 gDir = currentShip
                     ? currentShip.GetArtificialGravityDirection()
                     : gravityAffector.GetGravityDirection();

        rb.AddForce(-gDir * jumpForce, ForceMode.Impulse);
    }

    /* ── Jet-pack fuel ───────────────────────────────────────── */

    void UpdateJetpackFuel()
    {
        if (usingJetpack)
        {
            jetpackFuelPercent = Mathf.Max(0f, jetpackFuelPercent -
                                           (1f / jetpackDuration) * Time.deltaTime);
            lastJetpackUseTime = Time.time;
        }
        else if (Time.time - lastJetpackUseTime > jetpackRefuelDelay)
        {
            jetpackFuelPercent = Mathf.Min(1f, jetpackFuelPercent +
                                           (1f / jetpackRefuelTime) * Time.deltaTime);
        }
    }

    /* ── Gravity alignment ───────────────────────────────────── */

    void AlignToGravity(Vector3 gDir)
    {
        Vector3 gravityUp = -gDir.normalized;
        Vector3 currentUp = transform.up;

        // Only align if we're significantly misaligned with gravity
        float alignment = Vector3.Dot(currentUp, gravityUp);
        if (alignment > 0.95f) return; // Already well aligned

        // Calculate the rotation needed to align up vectors without changing forward direction
        Quaternion gravityAlignment = Quaternion.FromToRotation(currentUp, gravityUp);

        // Apply only a small portion of the alignment to avoid fighting with mouse look
        Quaternion partialAlignment = Quaternion.Slerp(Quaternion.identity, gravityAlignment,
                                                       Time.deltaTime * 2f);

        transform.rotation = partialAlignment * transform.rotation;

        if (modelTransform) modelTransform.localRotation = Quaternion.identity;
    }

    void ResetOrientationToPlanet()
    {
        if (currentShip != null) return; // Only for planetary gravity

        // Get gravity direction (points toward planet center)
        Vector3 gDir = gravityAffector.GetGravityDirection();

        // Calculate "up" direction (away from planet center)
        Vector3 gravityUp = -gDir.normalized;

        Debug.Log($"[ResetOrientation] Gravity direction: {gDir}");
        Debug.Log($"[ResetOrientation] Calculated up: {gravityUp}");
        Debug.Log($"[ResetOrientation] Current player up: {transform.up}");
        Debug.Log($"[ResetOrientation] Current player rotation: {transform.rotation.eulerAngles}");

        // Use a simple approach: make the player's up vector align with gravity up
        // while preserving the general forward direction
        Vector3 currentForward = transform.forward;
        Vector3 targetForward = Vector3.ProjectOnPlane(currentForward, gravityUp);

        // If forward is parallel to up, use world forward as fallback
        if (targetForward.sqrMagnitude < 0.001f)
        {
            targetForward = Vector3.ProjectOnPlane(Vector3.forward, gravityUp);
            Debug.Log("[ResetOrientation] Using fallback forward direction");
        }

        // Create target rotation
        Quaternion targetRotation = Quaternion.LookRotation(targetForward.normalized, gravityUp);

        Debug.Log($"[ResetOrientation] Target rotation: {targetRotation.eulerAngles}");

        // Apply the rotation
        transform.rotation = targetRotation;

        // Reset camera rotation variables to match new orientation
        Vector3 newEulers = transform.rotation.eulerAngles;
        yaw = newEulers.y;
        pitch = 0f; // Reset pitch to look straight ahead
        smoothYaw = yaw;
        smoothPitch = pitch;
        previousSmoothYaw = smoothYaw;

        // Reset camera to neutral position
        cameraTransform.localEulerAngles = Vector3.zero;

        Debug.Log($"[ResetOrientation] Final player rotation: {transform.rotation.eulerAngles}");
        Debug.Log($"[ResetOrientation] Reset complete - yaw: {yaw}, pitch: {pitch}");
    }


    /* ── Ship velocity inheritance ──────────────────────────── */

    bool IsShipMovementSyncActive()
    {
        ShipMovementSync shipSync = FindFirstObjectByType<ShipMovementSync>();
        return shipSync && shipSync.enabled && shipSync.gameObject.activeInHierarchy;
    }

    void InheritShipVelocity()
    {
        if (!currentShip) return;

        Rigidbody shipRb = currentShip.GetComponent<Rigidbody>();
        if (!shipRb) return;

        Vector3 shipVelocity = shipRb.linearVelocity;
        Vector3 shipVelocityDelta = shipVelocity - lastShipVelocity;

        rb.linearVelocity += shipVelocityDelta;
        lastShipVelocity = shipVelocity;
    }

    /* ── Ship hooks ──────────────────────────────────────────── */

    public void EnterShipInterior(Ship ship)
    {
        if (currentShip == ship) return;

        currentShip = ship;
        gravityAffector.SwitchToInteriorGravity(ship.GetComponent<ShipGravity>());
        ResetCameraRotationForShip();

        if (!IsShipMovementSyncActive())
        {
            Rigidbody shipRb = ship.GetComponent<Rigidbody>();
            if (shipRb)
            {
                Vector3 playerWorldVelocity = rb.linearVelocity;
                lastShipVelocity = shipRb.linearVelocity;

                Vector3 relative = playerWorldVelocity - shipRb.linearVelocity;
                rb.linearVelocity = shipRb.linearVelocity + Vector3.ClampMagnitude(relative, 10f);
            }
        }
    }

    void ResetCameraRotationForShip()
    {
        Vector3 camEuler = cameraTransform.localEulerAngles;
        yaw = camEuler.y;
        pitch = camEuler.x;
        if (pitch > 180f) pitch -= 360f;

        smoothYaw = yaw;
        smoothPitch = pitch;
        previousSmoothYaw = smoothYaw;
    }

    public void ExitFromSpaceship()
    {
        Debug.Log($"[PlayerController] Exiting spaceship - current ship: {currentShip}");
        Debug.Log($"[PlayerController] Player velocity before exit: {rb.linearVelocity}");

        gravityAffector.SwitchToPlanetaryGravity();
        currentShip = null;
        lastShipVelocity = Vector3.zero;

        // Clamp velocity to prevent weird fast movement when exiting ship
        Vector3 currentVel = rb.linearVelocity;
        if (currentVel.magnitude > 10f)
        {
            rb.linearVelocity = currentVel.normalized * 10f;
            Debug.Log($"[PlayerController] Clamped excessive velocity: {currentVel.magnitude:F1} -> {rb.linearVelocity.magnitude:F1}");
        }

        ResetCameraRotationForShip();

        // Auto-reset orientation to planet surface after a short delay
        // (delay allows gravity to switch properly first)
        Invoke(nameof(ResetOrientationToPlanet), 0.2f);

        Debug.Log($"[PlayerController] Ship exit complete");
    }

    /* ── Public conveniences ─────────────────────────────────── */
    public Camera Camera => cameraTransform.GetComponent<Camera>();
    public Rigidbody Rigidbody => rb;
}
