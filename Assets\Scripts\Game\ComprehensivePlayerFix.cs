using UnityEngine;

/// <summary>
/// Comprehensive fix for all player controller issues after friction fix implementation
/// </summary>
public class ComprehensivePlayerFix : MonoBehaviour
{
    [Header("Auto-fix on Start")]
    [SerializeField] private bool autoFixOnStart = true;

    [Header("Fix Options")]
    [SerializeField] private bool fixGravitySystem = true;
    [SerializeField] private bool fixInputSystem = true;
    [SerializeField] private bool fixGroundDetection = true;
    [SerializeField] private bool fixShipGravity = true;

    [Header("Debug")]
    [SerializeField] private bool enableDebugLogs = true;

    private PlayerController player;
    private GravityAffector gravityAffector;
    private Ship ship;

    void Start()
    {
        if (autoFixOnStart)
        {
            FindComponents();
            ApplyAllFixes();
        }
    }

    void FindComponents()
    {
        player = FindObjectOfType<PlayerController>();
        ship = FindObjectOfType<Ship>();

        if (player != null)
        {
            gravityAffector = player.GetComponent<GravityAffector>();
        }

        if (enableDebugLogs)
        {
            Debug.Log($"[ComprehensivePlayerFix] Found - Player: {player != null}, Ship: {ship != null}, GravityAffector: {gravityAffector != null}");
        }
    }

    [ContextMenu("Apply All Fixes")]
    public void ApplyAllFixes()
    {
        if (player == null) FindComponents();

        if (player == null)
        {
            Debug.LogError("[ComprehensivePlayerFix] Player not found!");
            return;
        }

        if (enableDebugLogs)
        {
            Debug.Log("[ComprehensivePlayerFix] Applying comprehensive fixes...");
        }

        if (fixInputSystem) FixInputSystem();
        if (fixGravitySystem) FixGravitySystem();
        if (fixGroundDetection) FixGroundDetection();
        if (fixShipGravity) FixShipGravity();

        if (enableDebugLogs)
        {
            Debug.Log("[ComprehensivePlayerFix] All fixes applied!");
        }
    }

    void FixInputSystem()
    {
        // Fix cursor lock
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;

        // Ensure input settings exist and are properly configured
        if (player.inputSettings == null)
        {
            player.inputSettings = ScriptableObject.CreateInstance<InputSettings>();
            player.inputSettings.mouseSensitivity = 100f;
            player.inputSettings.mouseSmoothing = 0.2f;
            player.inputSettings.lockCursor = true;
        }

        // Reinitialize input settings
        player.inputSettings.Begin();

        if (enableDebugLogs)
        {
            Debug.Log("[ComprehensivePlayerFix] ✅ Fixed input system");
        }
    }

    void FixGravitySystem()
    {
        if (gravityAffector == null)
        {
            Debug.LogError("[ComprehensivePlayerFix] GravityAffector not found!");
            return;
        }

        // Reset gravity system
        gravityAffector.SwitchToPlanetaryGravity();

        // Ensure rigidbody settings are correct
        Rigidbody rb = player.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.useGravity = false;
            rb.mass = 70f;
            rb.linearDamping = 0f;
            rb.angularDamping = 0.05f;
        }

        if (enableDebugLogs)
        {
            Debug.Log("[ComprehensivePlayerFix] ✅ Fixed gravity system");
        }
    }

    void FixGroundDetection()
    {
        // Ensure ground check exists
        if (player.groundCheck == null)
        {
            Transform feet = player.transform.Find("Feet");
            if (feet == null)
            {
                // Create feet transform if it doesn't exist
                GameObject feetObj = new GameObject("Feet");
                feetObj.transform.SetParent(player.transform);
                feetObj.transform.localPosition = new Vector3(0, -1f, 0);
                player.groundCheck = feetObj.transform;
            }
            else
            {
                player.groundCheck = feet;
            }
        }

        // Adjust ground check settings for better detection
        if (player.groundCheckDistance < 0.1f)
        {
            player.groundCheckDistance = 0.15f;
        }

        // Ensure ground mask includes appropriate layers
        if (player.groundMask.value == 0)
        {
            // Set to include Default, Ship, and Body layers
            player.groundMask = (1 << 0) | (1 << 9) | (1 << 10); // Default, Ship, Body
        }

        if (enableDebugLogs)
        {
            Debug.Log("[ComprehensivePlayerFix] ✅ Fixed ground detection");
        }
    }

    void FixShipGravity()
    {
        if (ship == null) return;

        // Ensure ship has proper gravity component
        ShipGravity shipGravity = ship.GetComponentInChildren<ShipGravity>();
        if (shipGravity == null)
        {
            Debug.LogWarning("[ComprehensivePlayerFix] Ship gravity component not found!");
            return;
        }

        // Ensure ship gravity trigger is properly configured
        Collider gravityTrigger = shipGravity.GetComponent<Collider>();
        if (gravityTrigger != null && !gravityTrigger.isTrigger)
        {
            gravityTrigger.isTrigger = true;
            if (enableDebugLogs)
            {
                Debug.Log("[ComprehensivePlayerFix] Fixed ship gravity trigger");
            }
        }

        if (enableDebugLogs)
        {
            Debug.Log("[ComprehensivePlayerFix] ✅ Fixed ship gravity system");
        }
    }

    /// <summary>
    /// Test all systems to verify fixes worked
    /// </summary>
    [ContextMenu("Test All Systems")]
    public void TestAllSystems()
    {
        if (player == null) FindComponents();

        Debug.Log("=== SYSTEM TEST RESULTS ===");

        // Test input
        float mouseX = Input.GetAxisRaw("Mouse X");
        float mouseY = Input.GetAxisRaw("Mouse Y");
        Debug.Log($"Mouse Input - X: {mouseX}, Y: {mouseY}, Cursor Locked: {Cursor.lockState == CursorLockMode.Locked}");

        // Test gravity
        if (gravityAffector != null)
        {
            Vector3 gravityDir = gravityAffector.GetGravityDirection();
            Debug.Log($"Gravity Direction: {gravityDir}, Magnitude: {gravityDir.magnitude}");
        }

        // Test ground detection
        if (player.groundCheck != null)
        {
            bool grounded = Physics.CheckSphere(player.groundCheck.position, player.groundCheckDistance, player.groundMask);
            Debug.Log($"Ground Check: {grounded}, Position: {player.groundCheck.position}");
        }

        // Test rigidbody
        Rigidbody rb = player.GetComponent<Rigidbody>();
        if (rb != null)
        {
            Debug.Log($"Rigidbody - Velocity: {rb.linearVelocity}, UseGravity: {rb.useGravity}");
        }

        Debug.Log("=== END SYSTEM TEST ===");
    }

    /// <summary>
    /// Emergency reset for when player gets stuck
    /// </summary>
    [ContextMenu("Emergency Player Reset")]
    public void EmergencyPlayerReset()
    {
        if (player == null) return;

        Rigidbody rb = player.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        // Reset to planetary gravity
        if (gravityAffector != null)
        {
            gravityAffector.SwitchToPlanetaryGravity();
        }

        // Reset cursor
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;

        Debug.Log("[ComprehensivePlayerFix] Emergency reset applied!");
    }
}
