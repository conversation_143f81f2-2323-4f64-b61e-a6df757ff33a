using UnityEngine;

/// <summary>
/// Test script to verify the ship gravity orientation fix works correctly
/// </summary>
public class ShipOrientationTester : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private KeyCode testKey = KeyCode.O;
    [SerializeField] private bool enableContinuousTest = false;
    
    [Header("Visualization")]
    [SerializeField] private bool showGizmos = true;
    [SerializeField] private float arrowLength = 2f;
    
    private PlayerController player;
    private Ship ship;
    private bool lastPilotState = false;
    
    void Start()
    {
        player = FindObjectOfType<PlayerController>();
        ship = FindObjectOfType<Ship>();
    }
    
    void Update()
    {
        if (Input.GetKeyDown(testKey))
        {
            TestShipOrientation();
        }
        
        if (enableContinuousTest)
        {
            MonitorPilotStateChanges();
        }
    }
    
    void MonitorPilotStateChanges()
    {
        if (ship == null) return;
        
        bool currentPilotState = ship.IsPiloted;
        
        // Detect when player exits pilot mode
        if (lastPilotState && !currentPilotState)
        {
            Debug.Log("[ShipOrientationTester] Player exited pilot mode - testing orientation...");
            Invoke(nameof(TestPlayerOrientationAfterExit), 0.1f); // Small delay to let physics settle
        }
        
        lastPilotState = currentPilotState;
    }
    
    void TestPlayerOrientationAfterExit()
    {
        if (player == null || ship == null) return;
        
        Debug.Log("=== POST-EXIT ORIENTATION TEST ===");
        
        Vector3 playerUp = player.transform.up;
        Vector3 shipUp = ship.transform.up;
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        
        Debug.Log($"Player up: {playerUp}");
        Debug.Log($"Ship up: {shipUp}");
        Debug.Log($"Ship gravity direction: {shipGravityDir}");
        
        // Test alignment
        float playerShipAlignment = Vector3.Dot(playerUp, shipUp);
        Debug.Log($"Player-Ship up alignment: {playerShipAlignment}");
        
        if (playerShipAlignment > 0.9f)
        {
            Debug.Log("✅ Player is correctly oriented to ship!");
        }
        else if (playerShipAlignment < -0.9f)
        {
            Debug.Log("❌ Player is upside down relative to ship!");
        }
        else
        {
            Debug.Log("⚠️ Player orientation is off-axis from ship!");
        }
        
        // Test if player is standing on ship floor
        Vector3 gravityUp = -shipGravityDir;
        float gravityAlignment = Vector3.Dot(playerUp, gravityUp);
        Debug.Log($"Player-Gravity up alignment: {gravityAlignment}");
        
        if (gravityAlignment > 0.9f)
        {
            Debug.Log("✅ Player is standing upright against gravity!");
        }
        else
        {
            Debug.Log("❌ Player is not properly aligned with gravity!");
        }
        
        Debug.Log("=== END POST-EXIT TEST ===");
    }
    
    [ContextMenu("Test Ship Orientation")]
    public void TestShipOrientation()
    {
        if (player == null || ship == null)
        {
            Debug.LogError("Player or Ship not found!");
            return;
        }
        
        Debug.Log("=== SHIP ORIENTATION TEST ===");
        
        // Ship information
        Vector3 shipPos = ship.transform.position;
        Vector3 shipUp = ship.transform.up;
        Vector3 shipForward = ship.transform.forward;
        Vector3 shipRight = ship.transform.right;
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        
        Debug.Log($"Ship position: {shipPos}");
        Debug.Log($"Ship rotation: {ship.transform.rotation.eulerAngles}");
        Debug.Log($"Ship up: {shipUp}");
        Debug.Log($"Ship forward: {shipForward}");
        Debug.Log($"Ship right: {shipRight}");
        Debug.Log($"Ship gravity direction: {shipGravityDir}");
        
        // Player information
        Vector3 playerPos = player.transform.position;
        Vector3 playerUp = player.transform.up;
        Vector3 playerForward = player.transform.forward;
        
        Debug.Log($"Player position: {playerPos}");
        Debug.Log($"Player up: {playerUp}");
        Debug.Log($"Player forward: {playerForward}");
        
        // Alignment tests
        float upAlignment = Vector3.Dot(playerUp, shipUp);
        float gravityAlignment = Vector3.Dot(playerUp, -shipGravityDir);
        
        Debug.Log($"Player-Ship up alignment: {upAlignment}");
        Debug.Log($"Player-Gravity alignment: {gravityAlignment}");
        
        // Results
        if (upAlignment > 0.9f && gravityAlignment > 0.9f)
        {
            Debug.Log("✅ PERFECT: Player is correctly oriented!");
        }
        else if (upAlignment > 0.9f)
        {
            Debug.Log("✅ GOOD: Player aligns with ship up");
        }
        else if (gravityAlignment > 0.9f)
        {
            Debug.Log("✅ GOOD: Player aligns with gravity");
        }
        else
        {
            Debug.Log("❌ PROBLEM: Player orientation is incorrect!");
        }
        
        Debug.Log("=== END ORIENTATION TEST ===");
    }
    
    [ContextMenu("Test Gravity Direction Logic")]
    public void TestGravityDirectionLogic()
    {
        if (ship == null) return;
        
        Debug.Log("=== GRAVITY DIRECTION LOGIC TEST ===");
        
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        Vector3 shipTransformUp = ship.transform.up;
        Vector3 shipTransformDown = -ship.transform.up;
        
        Debug.Log($"Ship.GetArtificialGravityDirection(): {shipGravityDir}");
        Debug.Log($"Ship.transform.up: {shipTransformUp}");
        Debug.Log($"Ship.transform.down (-up): {shipTransformDown}");
        
        // Check which direction gravity points
        float dotWithUp = Vector3.Dot(shipGravityDir, shipTransformUp);
        float dotWithDown = Vector3.Dot(shipGravityDir, shipTransformDown);
        
        Debug.Log($"Gravity dot with ship up: {dotWithUp}");
        Debug.Log($"Gravity dot with ship down: {dotWithDown}");
        
        if (Mathf.Abs(dotWithDown) > Mathf.Abs(dotWithUp))
        {
            Debug.Log("✅ Gravity points toward ship floor (correct)");
        }
        else
        {
            Debug.Log("❌ Gravity points toward ship ceiling (incorrect)");
        }
        
        // Test the fix logic
        Vector3 playerUpShouldBe = ship.transform.up;
        Debug.Log($"Player up should be: {playerUpShouldBe}");
        Debug.Log($"This matches ship ceiling: {Vector3.Dot(playerUpShouldBe, shipTransformUp) > 0.99f}");
        
        Debug.Log("=== END GRAVITY LOGIC TEST ===");
    }
    
    void OnDrawGizmos()
    {
        if (!showGizmos || ship == null) return;
        
        Vector3 shipPos = ship.transform.position;
        
        // Draw ship axes
        Gizmos.color = Color.red;
        Gizmos.DrawRay(shipPos, ship.transform.right * arrowLength);
        
        Gizmos.color = Color.green;
        Gizmos.DrawRay(shipPos, ship.transform.up * arrowLength);
        
        Gizmos.color = Color.blue;
        Gizmos.DrawRay(shipPos, ship.transform.forward * arrowLength);
        
        // Draw gravity direction
        Vector3 gravityDir = ship.GetArtificialGravityDirection();
        Gizmos.color = Color.yellow;
        Gizmos.DrawRay(shipPos, gravityDir * arrowLength * 1.2f);
        
        // Draw player orientation if available
        if (player != null)
        {
            Vector3 playerPos = player.transform.position;
            
            Gizmos.color = Color.cyan;
            Gizmos.DrawRay(playerPos, player.transform.up * arrowLength * 0.8f);
            
            Gizmos.color = Color.magenta;
            Gizmos.DrawRay(playerPos, player.transform.forward * arrowLength * 0.6f);
        }
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 450, 400, 100));
        GUILayout.Label($"Press {testKey} to test ship orientation");
        GUILayout.Label("Gizmos: Red=Right, Green=Up, Blue=Forward, Yellow=Gravity");
        GUILayout.Label("Player: Cyan=Up, Magenta=Forward");
        GUILayout.EndArea();
    }
}
