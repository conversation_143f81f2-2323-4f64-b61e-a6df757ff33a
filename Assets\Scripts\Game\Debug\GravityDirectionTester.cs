using UnityEngine;

/// <summary>
/// Debug tool to test and visualize gravity directions to fix the Z-axis inversion issue
/// </summary>
public class GravityDirectionTester : MonoBehaviour
{
    [Header("Debug Visualization")]
    [SerializeField] private bool showGizmos = true;
    [SerializeField] private float arrowLength = 2f;
    [SerializeField] private Color shipGravityColor = Color.red;
    [SerializeField] private Color planetGravityColor = Color.blue;
    [SerializeField] private Color playerUpColor = Color.green;
    
    [Header("Test Controls")]
    [SerializeField] private KeyCode testKey = KeyCode.G;
    
    private PlayerController player;
    private Ship ship;
    private GravityAffector gravityAffector;
    
    void Start()
    {
        player = FindObjectOfType<PlayerController>();
        ship = FindObjectOfType<Ship>();
        if (player != null)
        {
            gravityAffector = player.GetComponent<GravityAffector>();
        }
    }
    
    void Update()
    {
        if (Input.GetKeyDown(testKey))
        {
            TestGravityDirections();
        }
    }
    
    void TestGravityDirections()
    {
        Debug.Log("=== GRAVITY DIRECTION TEST ===");
        
        if (player == null || ship == null)
        {
            Debug.LogError("Player or Ship not found!");
            return;
        }
        
        Vector3 playerPos = player.transform.position;
        
        // Test ship gravity
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        Vector3 shipTransformUp = ship.transform.up;
        Vector3 shipTransformDown = -ship.transform.up;
        
        Debug.Log($"Ship transform.up: {shipTransformUp}");
        Debug.Log($"Ship transform.down (-up): {shipTransformDown}");
        Debug.Log($"Ship GetArtificialGravityDirection(): {shipGravityDir}");
        Debug.Log($"Ship gravity matches -transform.up: {Vector3.Dot(shipGravityDir, shipTransformDown) > 0.99f}");
        
        // Test what the player should use as "up"
        Vector3 playerShouldFaceUp = -shipGravityDir;
        Debug.Log($"Player should face up: {playerShouldFaceUp}");
        Debug.Log($"Player up matches ship transform.up: {Vector3.Dot(playerShouldFaceUp, shipTransformUp) > 0.99f}");
        
        // Test current player orientation
        Vector3 playerCurrentUp = player.transform.up;
        Debug.Log($"Player current up: {playerCurrentUp}");
        Debug.Log($"Player up alignment with ship up: {Vector3.Dot(playerCurrentUp, shipTransformUp)}");
        
        // Test gravity affector
        if (gravityAffector != null)
        {
            Vector3 gravityAffectorDir = gravityAffector.GetGravityDirection();
            Debug.Log($"GravityAffector direction: {gravityAffectorDir}");
        }
        
        // Test what happens when ship rotates
        Debug.Log($"Ship rotation: {ship.transform.rotation.eulerAngles}");
        
        Debug.Log("=== END GRAVITY DIRECTION TEST ===");
    }
    
    void OnDrawGizmos()
    {
        if (!showGizmos || player == null || ship == null) return;
        
        Vector3 playerPos = player.transform.position;
        
        // Draw ship gravity direction (red arrow pointing toward floor)
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        Gizmos.color = shipGravityColor;
        Gizmos.DrawRay(playerPos, shipGravityDir * arrowLength);
        Gizmos.DrawWireSphere(playerPos + shipGravityDir * arrowLength, 0.1f);
        
        // Draw ship transform.up (what should be player up - green arrow)
        Vector3 shipUp = ship.transform.up;
        Gizmos.color = playerUpColor;
        Gizmos.DrawRay(playerPos, shipUp * arrowLength);
        Gizmos.DrawWireCube(playerPos + shipUp * arrowLength, Vector3.one * 0.2f);
        
        // Draw player's current up direction (yellow arrow)
        Gizmos.color = Color.yellow;
        Gizmos.DrawRay(playerPos, player.transform.up * arrowLength * 0.8f);
        
        // Draw ship transform axes for reference
        Gizmos.color = Color.red;
        Gizmos.DrawRay(ship.transform.position, ship.transform.right * 1f);
        Gizmos.color = Color.green;
        Gizmos.DrawRay(ship.transform.position, ship.transform.up * 1f);
        Gizmos.color = Color.blue;
        Gizmos.DrawRay(ship.transform.position, ship.transform.forward * 1f);
    }
    
    /// <summary>
    /// Test different gravity direction interpretations
    /// </summary>
    [ContextMenu("Test Gravity Interpretations")]
    public void TestGravityInterpretations()
    {
        if (ship == null) return;
        
        Vector3 shipGravityDir = ship.GetArtificialGravityDirection();
        
        Debug.Log("=== GRAVITY INTERPRETATION TEST ===");
        Debug.Log($"Ship gravity direction: {shipGravityDir}");
        Debug.Log($"Ship transform.up: {ship.transform.up}");
        Debug.Log($"Ship transform.down: {-ship.transform.up}");
        
        // Test interpretation 1: shipUp = -shipGravityDir
        Vector3 interpretation1 = -shipGravityDir;
        Debug.Log($"Interpretation 1 (-shipGravityDir): {interpretation1}");
        Debug.Log($"  Matches ship.transform.up: {Vector3.Dot(interpretation1, ship.transform.up) > 0.99f}");
        
        // Test interpretation 2: shipUp = shipGravityDir  
        Vector3 interpretation2 = shipGravityDir;
        Debug.Log($"Interpretation 2 (shipGravityDir): {interpretation2}");
        Debug.Log($"  Matches ship.transform.up: {Vector3.Dot(interpretation2, ship.transform.up) > 0.99f}");
        
        // Test interpretation 3: shipUp = ship.transform.up directly
        Vector3 interpretation3 = ship.transform.up;
        Debug.Log($"Interpretation 3 (ship.transform.up): {interpretation3}");
        
        Debug.Log("=== END INTERPRETATION TEST ===");
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 200, 300, 100));
        GUILayout.Label($"Press {testKey} to test gravity directions");
        GUILayout.Label("Red arrow: Ship gravity direction");
        GUILayout.Label("Green arrow: Ship transform.up");
        GUILayout.Label("Yellow arrow: Player current up");
        GUILayout.EndArea();
    }
}
